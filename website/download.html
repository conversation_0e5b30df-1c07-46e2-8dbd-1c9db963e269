<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载智车桌面 - 让您的车机更智能、更安全</title>
    <meta name="description" content="下载智车桌面应用，体验为驾驶场景设计的Android车载应用启动器，让驾驶更安全、便捷。">

    <!-- Favicon 设置 -->
    <link rel="icon" type="image/webp" href="images/logo.webp">
    <link rel="shortcut icon" type="image/webp" href="images/logo.webp">

    <!-- 性能优化: 优先加载关键资源 -->
    <link rel="preload" href="css/main.css" as="style">
    <link rel="preload" href="images/logo.webp" as="image" fetchpriority="high">
    <link rel="preload" href="js/main.js" as="script">

    <!-- 预加载核心字体 -->
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@400;500;600;700&display=swap" as="font" crossorigin>

    <!-- 模块化样式表 -->
    <link rel="stylesheet" href="css/main.css?v=2025011518">
    
    <!-- 性能优化: 异步加载非关键资源 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" media="print" onload="this.media='all'">
    
    <!-- PWA相关配置 -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#2563eb">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="智车桌面">
    <link rel="apple-touch-icon" href="images/logo.webp">
    
    <!-- 预连接第三方资源 -->
    <link rel="preconnect" href="https://cdnjs.cloudflare.com">
    <link rel="dns-prefetch" href="https://cdnjs.cloudflare.com">
</head>
<body class="scrollable-section">
    <!-- 顶部导航栏 -->
    <header class="glass-header">
        <div class="container">
            <div class="logo">
                <a href="index.html">
                    <img src="images/logo.webp" alt="智车桌面 Logo" width="40" height="40" fetchpriority="high">
                    <h1>智车桌面</h1>
                </a>
            </div>
            <nav>
                <ul class="nav-links">
                    <li><a href="index.html#value">核心优势</a></li>
                    <li><a href="index.html#features">功能特点</a></li>
                    <li><a href="index.html#showcase">界面展示</a></li>
                    <li><a href="index.html#testimonials">用户评价</a></li>
                    <li><a href="contact.html">联系我们</a></li>
                    <li><a href="download.html" class="nav-cta active">立即下载</a></li>
                </ul>
                <div class="hamburger">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            </nav>
        </div>
    </header>

    <!-- 下载页面主体 -->
    <section class="download-page">
        <div class="container">
            <div class="download-hero">
                <h1>下载智车桌面</h1>
                <p>选择适合您的下载方式，体验更智能的车机界面</p>
            </div>
            
            <div class="download-options-container">
                <div class="download-grid">
                    <div class="download-card main-download">
                        <div class="download-icon">
                            <i class="fab fa-android"></i>
                        </div>
                        <h3>智车桌面 标准版</h3>
                        <p>适用于大部分Android车机和设备的通用版本</p>
                        
                        <div class="download-options">
                            <a href="apk/smartcar_launcher_standard.apk" class="btn btn-download">
                            <i class="fas fa-download"></i> 立即下载
                        </a>
                            <button class="btn btn-qr" data-qr="standard">
                                <i class="fas fa-qrcode"></i> 扫码下载
                            </button>
                        </div>
                        
                        <div class="version-info">
                            <span>版本号: v3.5.2</span>
                            <span>文件大小: 8.7MB</span>
                            <span>兼容性: Android 4.2+</span>
                            <span>更新时间: 2025/06/25</span>
                        </div>
                        
                        <div class="qr-modal" id="qr-standard">
                            <div class="qr-content">
                                <h4>智车桌面标准版</h4>
                                <div class="qr-code">
                                    <img src="images/download-qr.svg" alt="标准版下载二维码" loading="lazy">
                                </div>
                                <p>使用手机扫码下载</p>
                                <button class="close-qr">&times;</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="download-card byd-download">
                        <div class="download-icon">
                            <i class="fas fa-car"></i>
                        </div>
                        <h3>智车桌面 比亚迪定制版</h3>
                        <p>专为比亚迪车机系统优化，完美适配DiLink系统</p>
                        
                        <div class="download-options">
                            <a href="apk/smartcar_launcher_byd.apk" class="btn btn-download btn-byd">
                                <i class="fas fa-download"></i> 立即下载
                            </a>
                            <button class="btn btn-qr btn-qr-byd" data-qr="byd">
                                <i class="fas fa-qrcode"></i> 扫码下载
                            </button>
                    </div>
                    
                        <div class="version-info">
                            <span>版本号: v3.5.2-BYD</span>
                            <span>文件大小: 9.2MB</span>
                            <span>兼容性: 比亚迪DiLink 3.0+</span>
                            <span>更新时间: 2025/06/25</span>
                        </div>
                        
                        <div class="qr-modal" id="qr-byd">
                            <div class="qr-content">
                                <h4>智车桌面比亚迪定制版</h4>
                        <div class="qr-code">
                                    <img src="images/download-qr.svg" alt="比亚迪版下载二维码" loading="lazy">
                                </div>
                                <p>使用手机扫码下载</p>
                                <button class="close-qr">&times;</button>
                        </div>
                        </div>
                    </div>
                </div>
                
                <div class="download-instructions">
                    <h3>安装说明</h3>
                    <div class="instructions-tabs">
                        <button class="tab-btn active" data-target="install-standard">标准安装</button>
                        <button class="tab-btn" data-target="install-car">车机安装</button>
                        <button class="tab-btn" data-target="install-tips">常见问题</button>
                    </div>
                    
                    <div id="install-standard" class="instructions-content active">
                        <ol>
                            <li>下载APK文件到您的Android设备</li>
                            <li>点击APK文件开始安装</li>
                            <li>如提示"未知来源"，请在设置中允许安装</li>
                            <li>安装完成后，点击"打开"即可使用</li>
                        </ol>
                    </div>
                    
                    <div id="install-car" class="instructions-content">
                        <ol>
                            <li>将APK文件拷贝到U盘</li>
                            <li>将U盘插入车机USB接口</li>
                            <li>在车机文件管理中找到并点击APK文件</li>
                            <li>按照屏幕提示完成安装</li>
                            <li>建议设置为默认启动器</li>
                        </ol>
                    </div>
                    
                    <div id="install-tips" class="instructions-content">
                        <ul>
                            <li><strong>无法安装？</strong> 确认您的Android版本是否支持（需Android 4.2以上）</li>
                            <li><strong>安装后闪退？</strong> 请确保给予应用所需权限</li>
                            <li><strong>设置为默认启动器：</strong> 设置 > 应用 > 默认应用 > 主屏幕应用</li>
                            <li><strong>需要帮助？</strong> <a href="contact.html">联系我们的技术支持</a></li>
                        </ul>
                    </div>
                </div>
                
                <div class="system-requirements">
                    <h3>系统要求</h3>
                    <div class="requirements-grid">
                        <div class="requirement-item">
                            <div class="requirement-icon">
                                <i class="fas fa-mobile-alt"></i>
                            </div>
                            <div class="requirement-info">
                                <h4>系统版本</h4>
                                <p>Android 4.2或更高版本</p>
                            </div>
                        </div>
                        
                        <div class="requirement-item">
                            <div class="requirement-icon">
                                <i class="fas fa-memory"></i>
                            </div>
                            <div class="requirement-info">
                                <h4>内存要求</h4>
                                <p>至少1GB RAM</p>
                            </div>
                        </div>
                        
                        <div class="requirement-item">
                            <div class="requirement-icon">
                                <i class="fas fa-hdd"></i>
                            </div>
                            <div class="requirement-info">
                                <h4>存储空间</h4>
                                <p>500MB可用存储空间</p>
                            </div>
                        </div>
                        
                        <div class="requirement-item">
                            <div class="requirement-icon">
                                <i class="fas fa-car"></i>
                            </div>
                            <div class="requirement-info">
                                <h4>兼容车型</h4>
                                <p>大部分Android系统车机</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 版本更新信息 -->
                <div class="version-info-section">
                    <h3>最新版本信息</h3>
                    <div class="latest-version-card">
                        <div class="version-header">
                            <div class="version-tag-large">v3.5.2</div>
                            <div class="version-date">2025年6月25日发布</div>
                        </div>
                        <div class="version-highlights">
                            <h4>本版本亮点</h4>
                            <ul class="highlight-list">
                                <li><i class="fas fa-star"></i> 全新UI设计，提升50%操作流畅度</li>
                                <li><i class="fas fa-shield-alt"></i> 增强安全性，支持指纹解锁</li>
                                <li><i class="fas fa-battery-three-quarters"></i> 优化电池使用，续航提升30%</li>
                                <li><i class="fas fa-wifi"></i> 改进网络连接稳定性</li>
                            </ul>
                        </div>
                    </div>
                </div>
                



            </div>
        </div>
    </section>

    <!-- 底部页脚 -->
    <footer></footer>

    <!-- 回到顶部按钮 -->
    <button id="back-to-top" class="back-to-top" aria-label="回到顶部">
        <i class="fas fa-chevron-up"></i>
    </button>

    <!-- 核心脚本 -->
    <script src="js/main.js" defer></script>
    <script src="js/site-header.js" defer></script>
    
    <!-- 下载页面专用脚本 -->
    <script src="js/download.js" defer></script>

    <!-- 统一页脚管理 -->
    <script src="js/site-footer.js" defer></script>

    <!-- Service Worker 注册 -->
    <script>
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', () => {
                navigator.serviceWorker.register('/service-worker.js')
                    .then(registration => {
                        console.log('Service Worker 注册成功:', registration);
                    })
                    .catch(error => {
                        console.log('Service Worker 注册失败:', error);
                    });
            });
        }
    </script>
<script src="livereload.js"></script>



</body>
</html> 